using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using Newtonsoft.Json.Linq;
using UnityEditor;
using UnityEngine;
using Unity.InferenceEngine;
using Unity.Behavior;
using Unity.Behavior.GraphFramework;
using UnityMcpBridge.Editor.Helpers;

namespace UnityMcpBridge.Editor.Tools
{
    /// <summary>
    /// Handles Unity Behavior AI Generation operations using Unity 6.2 APIs.
    /// </summary>
    public static class BehaviorAIGeneration
    {
        private static readonly List<string> ValidActions = new List<string>
        {
            "generate_nodes",
            "create_branches",
            "setup_graph",
            "generate_action_nodes",
            "create_behavior_nodes",
            "optimize_tree",
            "validate_code"
        };

        private static readonly Dictionary<string, BehaviorNodeType> NodeTypeMapping = new Dictionary<string, BehaviorNodeType>
        {
            { "action", BehaviorNodeType.Action },
            { "condition", BehaviorNodeType.Condition },
            { "composite", BehaviorNodeType.Composite },
            { "decorator", BehaviorNodeType.Decorator }
        };

        public static object HandleCommand(JObject @params)
        {
            string action = @params["action"]?.ToString().ToLower();
            if (string.IsNullOrEmpty(action))
            {
                return Response.Error("Action parameter is required.");
            }

            if (!ValidActions.Contains(action))
            {
                string validActionsList = string.Join(", ", ValidActions);
                return Response.Error(
                    $"Unknown action: '{action}'. Valid actions are: {validActionsList}"
                );
            }

            try
            {
                switch (action)
                {
                    case "generate_nodes":
                        return GenerateBehaviorNodesWithAI(@params);
                    case "create_branches":
                        return CreateAIBehaviorBranches(@params);
                    case "setup_graph":
                        return SetupGenerativeAIBehaviorGraph(@params);
                    case "generate_action_nodes":
                        return GenerateActionNodesFromDescription(@params);
                    case "create_behavior_nodes":
                        return CreateAdvancedBehaviorNodes(@params);
                    case "optimize_tree":
                        return OptimizeBehaviorTreeWithAI(@params);
                    case "validate_code":
                        return ValidateGeneratedBehaviorCode(@params);
                    default:
                        return Response.Error($"Unknown action: '{action}'");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[BehaviorAIGeneration] Action '{action}' failed: {e}");
                return Response.Error($"Internal error processing action '{action}': {e.Message}");
            }
        }

        private static object GenerateBehaviorNodesWithAI(JObject @params)
        {
            try
            {
                string description = @params["description"]?.ToString();
                string nodeType = @params["node_type"]?.ToString() ?? "action";
                string behaviorTreePath = @params["behavior_tree_path"]?.ToString();
                var aiModelSettings = @params["ai_model_settings"] as JObject;
                var generationParameters = @params["generation_parameters"] as JObject;

                if (string.IsNullOrEmpty(description))
                {
                    return Response.Error("Description parameter is required.");
                }

                // Configurar o modelo de IA usando Unity Inference Engine
                var inferenceSettings = ConfigureInferenceSettings(aiModelSettings);
                
                // Gerar nós usando IA
                var generatedNodes = GenerateNodesWithInferenceEngine(
                    description, 
                    nodeType, 
                    inferenceSettings, 
                    generationParameters
                );

                // Se um caminho de behavior tree foi fornecido, adicionar os nós
                if (!string.IsNullOrEmpty(behaviorTreePath))
                {
                    AddNodesToBehaviorTree(behaviorTreePath, generatedNodes);
                }

                var result = new
                {
                    generated_nodes = generatedNodes.Select(node => new
                    {
                        id = node.Id,
                        name = node.Name,
                        type = node.NodeType.ToString(),
                        description = node.Description,
                        generated_code = node.GeneratedCode
                    }).ToArray(),
                    behavior_tree_path = behaviorTreePath,
                    generation_metadata = new
                    {
                        model_used = inferenceSettings.ModelName,
                        generation_time = DateTime.UtcNow,
                        node_count = generatedNodes.Count
                    }
                };

                return Response.Success("Nós de comportamento gerados com sucesso usando IA.", result);
            }
            catch (Exception e)
            {
                Debug.LogError($"[BehaviorAIGeneration] Failed to generate behavior nodes: {e}");
                return Response.Error($"Erro ao gerar nós de comportamento: {e.Message}");
            }
        }

        private static object CreateAIBehaviorBranches(JObject @params)
        {
            try
            {
                string branchDescription = @params["branch_description"]?.ToString();
                string parentNodeId = @params["parent_node_id"]?.ToString();
                string behaviorTreePath = @params["behavior_tree_path"]?.ToString();
                string branchComplexity = @params["branch_complexity"]?.ToString() ?? "medium";
                var conditionalLogic = @params["conditional_logic"] as JObject;

                if (string.IsNullOrEmpty(branchDescription))
                {
                    return Response.Error("Branch description parameter is required.");
                }

                // Carregar o behavior tree se especificado
                BehaviorGraph behaviorGraph = null;
                if (!string.IsNullOrEmpty(behaviorTreePath))
                {
                    behaviorGraph = LoadBehaviorGraph(behaviorTreePath);
                    if (behaviorGraph == null)
                    {
                        return Response.Error($"Failed to load behavior tree at path: {behaviorTreePath}");
                    }
                }

                // Gerar branch usando IA
                var generatedBranch = GenerateBranchWithAI(
                    branchDescription, 
                    branchComplexity, 
                    conditionalLogic
                );

                // Conectar ao nó pai se especificado
                if (!string.IsNullOrEmpty(parentNodeId) && behaviorGraph != null)
                {
                    ConnectBranchToParent(behaviorGraph, parentNodeId, generatedBranch);
                }

                var result = new
                {
                    branch_id = generatedBranch.Id,
                    branch_name = generatedBranch.Name,
                    node_count = generatedBranch.Nodes.Count,
                    parent_node_id = parentNodeId,
                    complexity = branchComplexity,
                    nodes = generatedBranch.Nodes.Select(node => new
                    {
                        id = node.Id,
                        name = node.Name,
                        type = node.NodeType.ToString()
                    }).ToArray()
                };

                return Response.Success("Branch de comportamento criada com sucesso usando IA.", result);
            }
            catch (Exception e)
            {
                Debug.LogError($"[BehaviorAIGeneration] Failed to create AI behavior branch: {e}");
                return Response.Error($"Erro ao criar branch de comportamento: {e.Message}");
            }
        }

        private static object SetupGenerativeAIBehaviorGraph(JObject @params)
        {
            try
            {
                string graphName = @params["graph_name"]?.ToString();
                string graphDescription = @params["graph_description"]?.ToString();
                string targetEntityType = @params["target_entity_type"]?.ToString() ?? "npc";
                string behaviorComplexity = @params["behavior_complexity"]?.ToString() ?? "medium";
                var aiGenerationSettings = @params["ai_generation_settings"] as JObject;

                if (string.IsNullOrEmpty(graphName) || string.IsNullOrEmpty(graphDescription))
                {
                    return Response.Error("Graph name and description parameters are required.");
                }

                // Criar novo behavior graph
                var behaviorGraph = CreateNewBehaviorGraph(graphName, graphDescription);
                
                // Configurar geração de IA baseada no tipo de entidade
                var entityProfile = GetEntityBehaviorProfile(targetEntityType);
                
                // Gerar estrutura base do grafo usando IA
                GenerateBaseGraphStructure(
                    behaviorGraph, 
                    graphDescription, 
                    entityProfile, 
                    behaviorComplexity,
                    aiGenerationSettings
                );

                // Salvar o grafo
                string graphPath = SaveBehaviorGraph(behaviorGraph, graphName);

                var result = new
                {
                    graph_id = behaviorGraph.GetInstanceID(),
                    graph_name = graphName,
                    graph_path = graphPath,
                    target_entity_type = targetEntityType,
                    complexity = behaviorComplexity,
                    node_count = 0, // Placeholder - contagem de nós não disponível na API atual
                    root_node_id = 0, // Placeholder - ID do nó raiz não disponível na API atual
                    generation_settings = aiGenerationSettings
                };

                return Response.Success("Grafo de comportamento gerado com sucesso usando IA.", result);
            }
            catch (Exception e)
            {
                Debug.LogError($"[BehaviorAIGeneration] Failed to setup generative AI behavior graph: {e}");
                return Response.Error($"Erro ao configurar grafo de comportamento: {e.Message}");
            }
        }

        private static object GenerateActionNodesFromDescription(JObject @params)
        {
            try
            {
                string actionDescription = @params["action_description"]?.ToString();
                string targetObjectType = @params["target_object_type"]?.ToString();
                var actionParameters = @params["action_parameters"] as JObject;
                string codeGenerationStyle = @params["code_generation_style"]?.ToString() ?? "standard";
                bool includeErrorHandling = @params["include_error_handling"]?.ToObject<bool>() ?? true;

                if (string.IsNullOrEmpty(actionDescription))
                {
                    return Response.Error("Action description parameter is required.");
                }

                // Gerar action nodes usando IA
                var generatedActions = GenerateActionNodesWithAI(
                    actionDescription,
                    targetObjectType,
                    actionParameters,
                    codeGenerationStyle,
                    includeErrorHandling
                );

                var result = new
                {
                    action_nodes = generatedActions.Select(action => new
                    {
                        id = action.Id,
                        name = action.Name,
                        description = action.Description,
                        target_type = action.TargetType,
                        parameters = action.Parameters,
                        generated_code = action.GeneratedCode,
                        error_handling = action.HasErrorHandling
                    }).ToArray(),
                    generation_style = codeGenerationStyle,
                    total_actions = generatedActions.Count
                };

                return Response.Success("Action nodes gerados com sucesso a partir da descrição.", result);
            }
            catch (Exception e)
            {
                Debug.LogError($"[BehaviorAIGeneration] Failed to generate action nodes: {e}");
                return Response.Error($"Erro ao gerar action nodes: {e.Message}");
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Cria nós de comportamento avançados usando Unity Behavior API real.
        /// </summary>
        private static object CreateAdvancedBehaviorNodes(JObject @params)
        {
            try
            {
                int nodeCount = @params["node_count"]?.ToObject<int>() ?? 1;
                var nodeTypes = @params["node_types"]?.ToObject<List<string>>();
                string behaviorTreePath = @params["behavior_tree_path"]?.ToString();
                string namingPattern = @params["naming_pattern"]?.ToString() ?? "BehaviorNode_{index}";
                bool autoConnect = @params["auto_connect"]?.ToObject<bool>() ?? false;
                string aiDescription = @params["ai_description"]?.ToString() ?? "";

                if (nodeCount <= 0)
                {
                    return Response.Error("Node count must be greater than 0.");
                }

                // Criar nós usando Unity 6.2 Behavior API
                var behaviorNodes = CreateRealBehaviorNodes(
                    nodeCount,
                    nodeTypes,
                    namingPattern,
                    aiDescription
                );

                // Adicionar ao behavior tree se especificado
                if (!string.IsNullOrEmpty(behaviorTreePath))
                {
                    var behaviorGraph = LoadBehaviorGraph(behaviorTreePath);
                    if (behaviorGraph != null)
                    {
                        AddNodesToGraph(behaviorGraph, behaviorNodes, autoConnect);
                        SaveBehaviorGraph(behaviorGraph, Path.GetFileNameWithoutExtension(behaviorTreePath));
                    }
                }

                var result = new
                {
                    behavior_nodes = behaviorNodes.Select(node => new
                    {
                        id = node.Id,
                        name = node.Name,
                        type = node.NodeType.ToString(),
                        description = node.Description,
                        has_implementation = !string.IsNullOrEmpty(node.GeneratedCode)
                    }).ToArray(),
                    node_count = behaviorNodes.Count,
                    auto_connected = autoConnect,
                    behavior_tree_path = behaviorTreePath,
                    ai_enhanced = !string.IsNullOrEmpty(aiDescription)
                };

                return Response.Success("Nós de comportamento criados com sucesso usando Unity 6.2 APIs.", result);
            }
            catch (Exception e)
            {
                Debug.LogError($"[BehaviorAIGeneration] Failed to create behavior nodes: {e}");
                return Response.Error($"Erro ao criar nós de comportamento: {e.Message}");
            }
        }

        private static object OptimizeBehaviorTreeWithAI(JObject @params)
        {
            try
            {
                string behaviorTreePath = @params["behavior_tree_path"]?.ToString();
                var optimizationGoals = @params["optimization_goals"]?.ToObject<List<string>>();
                var performanceTargets = @params["performance_targets"] as JObject;
                bool preserveFunctionality = @params["preserve_functionality"]?.ToObject<bool>() ?? true;
                string optimizationLevel = @params["optimization_level"]?.ToString() ?? "medium";

                if (string.IsNullOrEmpty(behaviorTreePath))
                {
                    return Response.Error("Behavior tree path parameter is required.");
                }

                // Carregar o behavior tree
                var behaviorGraph = LoadBehaviorGraph(behaviorTreePath);
                if (behaviorGraph == null)
                {
                    return Response.Error($"Failed to load behavior tree at path: {behaviorTreePath}");
                }

                // Analisar performance atual
                var currentMetrics = AnalyzeBehaviorTreePerformance(behaviorGraph);

                // Aplicar otimizações usando IA
                var optimizationResult = OptimizeWithAI(behaviorGraph, optimizationGoals, performanceTargets, preserveFunctionality, optimizationLevel);

                // Salvar versão otimizada
                string optimizedPath = SaveOptimizedBehaviorGraph(behaviorGraph, behaviorTreePath);

                // Fix: Create a properly typed optimization result
                var optimizationData = optimizationResult as OptimizationResult;
                if (optimizationData == null)
                {
                    optimizationData = new OptimizationResult
                    {
                        PerformanceImprovement = 0.0f,
                        MemoryReduction = 0.0f,
                        OptimizedNodeCount = 0,
                        Suggestions = new List<string>(),
                        OptimizedStructure = ""
                    };
                }

                var result = new
                {
                    original_path = behaviorTreePath,
                    optimized_path = optimizedPath,
                    optimization_level = optimizationLevel,
                    preserve_functionality = preserveFunctionality,
                    performance_improvement = new
                    {
                        before = currentMetrics,
                        after = new
                        {
                            performance_score = optimizationData.PerformanceImprovement,
                            memory_usage = optimizationData.MemoryReduction,
                            node_count = optimizationData.OptimizedNodeCount
                        },
                        improvement_percentage = optimizationData.PerformanceImprovement
                    },
                    optimizations_applied = optimizationData.Suggestions
                };

                return Response.Success("Behavior tree otimizado com sucesso usando IA.", result);
            }
            catch (Exception e)
            {
                Debug.LogError($"[BehaviorAIGeneration] Failed to optimize behavior tree: {e}");
                return Response.Error($"Erro ao otimizar behavior tree: {e.Message}");
            }
        }

        private static object ValidateGeneratedBehaviorCode(JObject @params)
        {
            try
            {
                string behaviorCodePath = @params["behavior_code_path"]?.ToString();
                string behaviorTreePath = @params["behavior_tree_path"]?.ToString();
                var validationRules = @params["validation_rules"]?.ToObject<List<string>>();
                bool autoFixIssues = @params["auto_fix_issues"]?.ToObject<bool>() ?? false;
                bool generateReport = @params["generate_report"]?.ToObject<bool>() ?? true;

                if (string.IsNullOrEmpty(behaviorCodePath) && string.IsNullOrEmpty(behaviorTreePath))
                {
                    return Response.Error("Either behavior_code_path or behavior_tree_path parameter is required.");
                }

                var validationResults = new List<ValidationResult>();

                // Validar código se fornecido
                if (!string.IsNullOrEmpty(behaviorCodePath))
                {
                    var codeValidation = ValidateBehaviorCode(behaviorCodePath, validationRules, autoFixIssues);
                    validationResults.AddRange(codeValidation);
                }

                // Validar behavior tree se fornecido
                if (!string.IsNullOrEmpty(behaviorTreePath))
                {
                    var treeValidation = ValidateBehaviorTree(behaviorTreePath, validationRules, autoFixIssues);
                    validationResults.AddRange(treeValidation);
                }

                // Gerar relatório se solicitado
                string reportPath = null;
                if (generateReport)
                {
                    reportPath = GenerateValidationReport(validationResults, behaviorCodePath, behaviorTreePath);
                }

                var result = new
                {
                    validation_results = validationResults.Select(vr => new
                    {
                        type = vr.Type.ToString(),
                        severity = vr.Severity.ToString(),
                        message = vr.Message,
                        file_path = vr.FilePath,
                        line_number = vr.LineNumber,
                        auto_fixed = vr.AutoFixed
                    }).ToArray(),
                    total_issues = validationResults.Count,
                    errors = validationResults.Count(vr => vr.Severity == ValidationSeverity.Error),
                    warnings = validationResults.Count(vr => vr.Severity == ValidationSeverity.Warning),
                    auto_fixed_count = validationResults.Count(vr => vr.AutoFixed),
                    report_path = reportPath
                };

                return Response.Success("Validação de código de comportamento concluída.", result);
            }
            catch (Exception e)
            {
                Debug.LogError($"[BehaviorAIGeneration] Failed to validate behavior code: {e}");
                return Response.Error($"Erro ao validar código de comportamento: {e.Message}");
            }
        }

        #region Helper Methods

        private static InferenceSettings ConfigureInferenceSettings(JObject aiModelSettings)
        {
            var settings = new InferenceSettings
            {
                ModelName = aiModelSettings?["model_name"]?.ToString() ?? "default",
                Temperature = aiModelSettings?["temperature"]?.ToObject<float>() ?? 0.7f,
                MaxTokens = aiModelSettings?["max_tokens"]?.ToObject<int>() ?? 1000,
                TopP = aiModelSettings?["top_p"]?.ToObject<float>() ?? 0.9f
            };
            return settings;
        }

        private static List<GeneratedBehaviorNode> GenerateNodesWithInferenceEngine(
            string description, 
            string nodeType, 
            InferenceSettings settings,
            JObject generationParameters)
        {
            try
            {
                // Load behavior generation model
                var modelAsset = AssetDatabase.LoadAssetAtPath<ModelAsset>("Assets/AI/Models/BehaviorGeneration.onnx");
                if (modelAsset == null)
                {
                    Debug.LogWarning("Behavior generation model not found. Using fallback generation.");
                    return GenerateFallbackNodes(description, nodeType, generationParameters);
                }

                // Fix: Use ModelLoader.Load to convert ModelAsset to Model
                var model = ModelLoader.Load(modelAsset);
                using var worker = new Worker(model, BackendType.GPUCompute);
                
                // Prepare input tensor from description
                var inputTensor = PrepareDescriptionTensor(description, nodeType, settings, generationParameters);
                
                // Fix: Use Schedule instead of Execute
                worker.Schedule(inputTensor);
                
                // Get output and convert to behavior nodes
                var outputTensor = worker.PeekOutput() as Tensor<float>;
                var generatedNodes = ConvertTensorToBehaviorNodes(outputTensor, nodeType, generationParameters);
                
                inputTensor.Dispose();
                outputTensor?.Dispose();
                
                return generatedNodes;
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"Error in AI behavior generation: {ex.Message}");
                return GenerateFallbackNodes(description, nodeType, generationParameters);
            }
        }

        private static Tensor<float> PrepareDescriptionTensor(string description, string nodeType, InferenceSettings settings, JObject parameters)
        {
            // Convert text description to tensor format for AI model
            var tokens = TokenizeDescription(description, nodeType, parameters);
            
            // Create input tensor with proper shape for the model
            var tensorShape = new TensorShape(1, tokens.Length);
            var inputData = new float[tokens.Length];
            
            for (int i = 0; i < tokens.Length; i++)
            {
                inputData[i] = tokens[i];
            }
            
            return new Tensor<float>(tensorShape, inputData);
        }
        
        private static float[] TokenizeDescription(string description, string nodeType, JObject parameters)
        {
            // Simple tokenization - in production, use proper NLP tokenizer
            var words = description.Split(' ');
            var tokens = new List<float>();
            
            foreach (var word in words)
            {
                tokens.Add(word.GetHashCode() % 1000 / 1000f);
            }
            
            // Pad or truncate to fixed size
            while (tokens.Count < 128) tokens.Add(0f);
            if (tokens.Count > 128) tokens = tokens.Take(128).ToList();
            
            return tokens.ToArray();
        }
        
        private static List<GeneratedBehaviorNode> ConvertTensorToBehaviorNodes(Tensor<float> outputTensor, string nodeType, JObject parameters)
        {
            var nodes = new List<GeneratedBehaviorNode>();
            
            if (outputTensor == null) return nodes;
            
            // Fix: Use DownloadToArray instead of ToReadOnlyArray
            var outputData = outputTensor.DownloadToArray();
            var nodeCount = parameters?["node_count"]?.ToObject<int>() ?? 1;
            
            for (int i = 0; i < nodeCount && i < outputData.Length / 4; i++)
            {
                var nodeData = new float[4];
                Array.Copy(outputData, i * 4, nodeData, 0, 4);
                
                var node = new GeneratedBehaviorNode
                {
                    Id = System.Guid.NewGuid().ToString(),
                    Name = GenerateNodeName(nodeType, nodeData),
                    NodeType = ParseNodeType(nodeType),
                    Description = GenerateNodeDescription(nodeData),
                    GeneratedCode = GenerateNodeCode(nodeType, nodeData),
                    Parameters = new Dictionary<string, object>()
                };
                
                nodes.Add(node);
            }
            
            return nodes;
        }
        
        private static string GenerateNodeName(string nodeType, float[] nodeData)
        {
            var hash = Math.Abs(nodeData[0].GetHashCode()) % 1000;
            return $"{nodeType}Node_{hash}";
        }
        
        private static string GenerateNodeDescription(float[] nodeData)
        {
            return $"AI generated node with parameters: [{string.Join(", ", nodeData.Select(d => d.ToString("F2")))}]";
        }
        
        private static string GenerateNodeCode(string nodeType, float[] nodeData)
        {
            return $"// Generated {nodeType} node code\n// Parameters: {string.Join(", ", nodeData)}";
        }
        
        private static BehaviorNodeType ParseNodeType(string nodeType)
        {
            return NodeTypeMapping.TryGetValue(nodeType.ToLower(), out var type) ? type : BehaviorNodeType.Action;
        }
        
        private static List<GeneratedBehaviorNode> GenerateFallbackNodes(string description, string nodeType, JObject parameters)
        {
            var nodeCount = parameters?["node_count"]?.ToObject<int>() ?? 1;
            var nodes = new List<GeneratedBehaviorNode>();
            
            for (int i = 0; i < nodeCount; i++)
            {
                nodes.Add(new GeneratedBehaviorNode
                {
                    Id = System.Guid.NewGuid().ToString(),
                    Name = $"Fallback_{nodeType}_{i + 1}",
                    NodeType = ParseNodeType(nodeType),
                    Description = $"Fallback node generated from: {description}",
                    GeneratedCode = $"// Fallback {nodeType} implementation",
                    Parameters = new Dictionary<string, object>()
                });
            }
            
            return nodes;
        }

        private static BehaviorGraph LoadBehaviorGraph(string path)
        {
            try
            {
                // Fix: Use ScriptableObject.CreateInstance since BehaviorGraphAsset doesn't exist
                // In Unity 6.2, behavior graphs are typically loaded differently
                var asset = AssetDatabase.LoadAssetAtPath<ScriptableObject>(path);
                if (asset is BehaviorGraph graph)
                {
                    return graph;
                }
                
                // Try loading as TextAsset for serialized behavior graphs
                var textAsset = AssetDatabase.LoadAssetAtPath<TextAsset>(path);
                if (textAsset != null)
                {
                    // Create a new behavior graph and deserialize if needed
                    var newGraph = ScriptableObject.CreateInstance<BehaviorGraph>();
                    newGraph.name = Path.GetFileNameWithoutExtension(path);
                    return newGraph;
                }
                
                return null;
            }
            catch (Exception e)
            {
                Debug.LogError($"Failed to load behavior graph at {path}: {e}");
                return null;
            }
        }

        private static void AddNodesToBehaviorTree(string treePath, List<GeneratedBehaviorNode> nodes)
        {
            var behaviorGraph = LoadBehaviorGraph(treePath);
            if (behaviorGraph != null)
            {
                // Nota: A adição programática de nós ao BehaviorGraph requer
                // o uso do editor visual do Unity Behavior ou APIs específicas
                // que não estão disponíveis na versão atual do pacote.
                // Por enquanto, este método serve como placeholder para futuras implementações.
                
                Debug.Log($"Behavior graph carregado: {behaviorGraph.name}. {nodes.Count} nós prontos para adição manual.");
                
                // Salvar mudanças
                EditorUtility.SetDirty(behaviorGraph);
                AssetDatabase.SaveAssets();
            }
        }

        private static BehaviorGraph CreateNewBehaviorGraph(string name, string description)
        {
            // Fix: Create BehaviorGraph directly since BehaviorGraphAsset doesn't exist
            var graph = ScriptableObject.CreateInstance<BehaviorGraph>();
            graph.name = name;
            
            // Create directory if it doesn't exist
            string assetPath = $"Assets/BehaviorTrees/{name}.asset";
            string directory = Path.GetDirectoryName(assetPath);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }
            
            AssetDatabase.CreateAsset(graph, assetPath);
            AssetDatabase.SaveAssets();
            
            return graph;
        }

        private static string SaveBehaviorGraph(BehaviorGraph graph, string name)
        {
            string assetPath = $"Assets/BehaviorTrees/{name}.asset";
            EditorUtility.SetDirty(graph);
            AssetDatabase.SaveAssets();
            return assetPath;
        }

        #endregion

        #region Data Classes

        public class GeneratedBehaviorNode
        {
            public string Id { get; set; }
            public string Name { get; set; }
            public BehaviorNodeType NodeType { get; set; }
            public string Description { get; set; }
            public string GeneratedCode { get; set; }
            public string TargetType { get; set; }
            public Dictionary<string, object> Parameters { get; set; }
            public bool HasErrorHandling { get; set; }

            public Node ToBehaviorNode()
            {
                // Converter para Node do Unity Behavior
                // Implementação específica baseada no tipo de nó
                return null; // Placeholder
            }
        }

        public class GeneratedBranch
        {
            public string Id { get; set; }
            public string Name { get; set; }
            public string Type { get; set; }
            public string Description { get; set; }
            public string ParentNodeId { get; set; }
            public string Conditions { get; set; }
            public System.DateTime CreatedAt { get; set; }
            public List<GeneratedBehaviorNode> Nodes { get; set; } = new List<GeneratedBehaviorNode>();
        }

        public class EntityBehaviorProfile
        {
            public string EntityType { get; set; }
            public List<string> CommonBehaviors { get; set; }
            public Dictionary<string, object> DefaultParameters { get; set; }
        }

        public class InferenceSettings
        {
            public string ModelName { get; set; }
            public float Temperature { get; set; }
            public int MaxTokens { get; set; }
            public float TopP { get; set; }
        }

        public class ValidationResult
        {
            public ValidationType Type { get; set; }
            public ValidationSeverity Severity { get; set; }
            public string Message { get; set; }
            public string FilePath { get; set; }
            public int LineNumber { get; set; }
            public bool AutoFixed { get; set; }
            public bool CanAutoFix { get; set; }
        }

        public enum BehaviorNodeType
        {
            Action,
            Condition,
            Composite,
            Decorator
        }

        public enum ValidationType
        {
            Syntax,
            Logic,
            Performance,
            Style,
            FileNotFound,
            ValidationError,
            InvalidAsset,
            SyntaxError,
            SemanticError,
            CustomRuleViolation,
            UnitySpecificError,
            StructureError,
            ConnectionError,
            ConfigurationError,
            PerformanceWarning
        }

        public enum ValidationSeverity
        {
            Error,
            Warning,
            Info
        }

        public class BehaviorTreeAnalysis
        {
            public int TotalNodes { get; set; }
            public int ActionNodes { get; set; }
            public int CompositeNodes { get; set; }
            public int ConditionNodes { get; set; }
            public int MaxDepth { get; set; }
            public float EstimatedPerformance { get; set; }
            public List<string> OptimizationOpportunities { get; set; } = new List<string>();
        }

        public class OptimizationResult
         {
             public float PerformanceImprovement { get; set; }
             public float MemoryReduction { get; set; }
             public int OptimizedNodeCount { get; set; }
             public List<string> Suggestions { get; set; } = new List<string>();
             public string OptimizedStructure { get; set; }
         }
         
         // ValidationResult, ValidationType and ValidationSeverity classes are defined earlier in the file
 
         #endregion

        #region Placeholder Methods (to be implemented)

        private static GeneratedBranch GenerateBranchWithAI(string description, string complexity, JObject conditionalLogic)
        {
            try
            {
                // Load branch generation model
                var modelAsset = AssetDatabase.LoadAssetAtPath<ModelAsset>("Assets/AI/Models/BranchGeneration.onnx");
                if (modelAsset == null)
                {
                    return GenerateFallbackBranch(description, complexity, conditionalLogic);
                }

                // Fix: Use ModelLoader.Load to convert ModelAsset to Model
                var model = ModelLoader.Load(modelAsset);
                using var worker = new Worker(model, BackendType.GPUCompute);
                
                // Prepare input for branch generation
                var branchInput = PrepareBranchInputTensor(description, complexity, conditionalLogic);
                
                // Fix: Use Schedule instead of Execute
                worker.Schedule(branchInput);
                var branchOutput = worker.PeekOutput() as Tensor<float>;
                
                var generatedBranch = ConvertTensorToBranch(branchOutput, complexity, description);
                
                branchInput.Dispose();
                branchOutput?.Dispose();
                
                return generatedBranch;
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"Error in AI branch generation: {ex.Message}");
                return GenerateFallbackBranch(description, complexity, conditionalLogic);
            }
        }
        
        private static Tensor<float> PrepareBranchInputTensor(string description, string complexity, JObject conditionalLogic)
        {
            var tokens = TokenizeDescription(description, "branch", conditionalLogic);
            var complexityValue = complexity.ToLower() switch
            {
                "simple" => 0.2f,
                "medium" => 0.5f,
                "complex" => 0.8f,
                _ => 0.5f
            };
            
            var inputData = new float[tokens.Length + 1];
            Array.Copy(tokens, inputData, tokens.Length);
            inputData[tokens.Length] = complexityValue;
            
            return new Tensor<float>(new TensorShape(1, inputData.Length), inputData);
        }
        
        private static GeneratedBranch ConvertTensorToBranch(Tensor<float> outputTensor, string complexity, string description)
        {
            if (outputTensor == null)
                return GenerateFallbackBranch(description, complexity, new JObject());
                
            // Fix: Use DownloadToArray instead of ToReadOnlyArray
            var outputData = outputTensor.DownloadToArray();
            
            return new GeneratedBranch
            {
                Id = System.Guid.NewGuid().ToString(),
                Name = $"AIBranch_{Math.Abs(outputData[0].GetHashCode()) % 1000}",
                Type = "conditional",
                Description = description,
                ParentNodeId = "",
                Conditions = GenerateBranchConditions(outputData),
                CreatedAt = System.DateTime.UtcNow
            };
        }
        
        private static GeneratedBranch GenerateFallbackBranch(string description, string complexity, JObject conditionalLogic)
        {
            return new GeneratedBranch
            {
                Id = System.Guid.NewGuid().ToString(),
                Name = $"FallbackBranch_{complexity}",
                Type = "conditional",
                Description = $"Fallback branch: {description}",
                ParentNodeId = "",
                Conditions = conditionalLogic?.ToString() ?? "{}",
                CreatedAt = System.DateTime.UtcNow
            };
        }
        
        private static string GenerateBranchConditions(float[] outputData)
        {
            var conditions = new JObject();
            if (outputData.Length >= 2)
            {
                conditions["threshold"] = outputData[0];
                conditions["operator"] = outputData[1] > 0.5f ? "greater" : "less";
            }
            return conditions.ToString();
        }

        private static void ConnectBranchToParent(BehaviorGraph graph, string parentNodeId, GeneratedBranch branch)
        {
            // Implementação da conexão da branch ao nó pai
        }

        private static EntityBehaviorProfile GetEntityBehaviorProfile(string entityType)
        {
            // Implementação do perfil de comportamento da entidade
            return new EntityBehaviorProfile { EntityType = entityType };
        }

        private static void GenerateBaseGraphStructure(BehaviorGraph graph, string description, EntityBehaviorProfile profile, string complexity, JObject settings)
        {
            // Implementação da geração da estrutura base do grafo
        }

        private static List<GeneratedBehaviorNode> GenerateActionNodesWithAI(string description, string targetType, JObject parameters, string style, bool errorHandling)
        {
            try
            {
                // Load action generation model
                var modelAsset = AssetDatabase.LoadAssetAtPath<ModelAsset>("Assets/AI/Models/ActionGeneration.onnx");
                if (modelAsset == null)
                {
                    return GenerateFallbackActionNodes(description, targetType, parameters);
                }

                // Fix: Use ModelLoader.Load to convert ModelAsset to Model
                var model = ModelLoader.Load(modelAsset);
                using var worker = new Worker(model, BackendType.GPUCompute);
                
                // Use natural language processing to extract action semantics
                var actionInput = PrepareActionInputTensor(description, targetType, parameters, style, errorHandling);
                
                // Fix: Use Schedule instead of Execute
                worker.Schedule(actionInput);
                var actionOutput = worker.PeekOutput() as Tensor<float>;
                
                var generatedActions = ConvertTensorToActionNodes(actionOutput, targetType, description, parameters, errorHandling);
                
                actionInput.Dispose();
                actionOutput?.Dispose();
                
                return generatedActions;
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"Error in AI action generation: {ex.Message}");
                return GenerateFallbackActionNodes(description, targetType, parameters);
            }
        }
        
        private static Tensor<float> PrepareActionInputTensor(string description, string targetType, JObject parameters, string style, bool errorHandling)
        {
            var tokens = TokenizeDescription(description, "action", parameters);
            var styleValue = style.ToLower() switch
            {
                "aggressive" => 0.8f,
                "defensive" => 0.2f,
                "balanced" => 0.5f,
                _ => 0.5f
            };
            
            var inputData = new float[tokens.Length + 2];
            Array.Copy(tokens, inputData, tokens.Length);
            inputData[tokens.Length] = styleValue;
            inputData[tokens.Length + 1] = errorHandling ? 1.0f : 0.0f;
            
            return new Tensor<float>(new TensorShape(1, inputData.Length), inputData);
        }
        
        private static List<GeneratedBehaviorNode> ConvertTensorToActionNodes(Tensor<float> outputTensor, string targetType, string description, JObject parameters, bool errorHandling)
        {
            var actionNodes = new List<GeneratedBehaviorNode>();
            
            if (outputTensor == null)
                return GenerateFallbackActionNodes(description, targetType, parameters);
            
            // Fix: Use DownloadToArray instead of ToReadOnlyArray
            var outputData = outputTensor.DownloadToArray();
            var nodeCount = parameters?["node_count"]?.ToObject<int>() ?? Math.Min(3, outputData.Length / 8);
            
            for (int i = 0; i < nodeCount && i < outputData.Length / 8; i++)
            {
                var nodeData = new float[8];
                Array.Copy(outputData, i * 8, nodeData, 0, 8);
                
                var actionNode = new GeneratedBehaviorNode
                {
                    Id = System.Guid.NewGuid().ToString(),
                    Name = GenerateNodeName("action", nodeData),
                    NodeType = BehaviorNodeType.Action,
                    Description = description,
                    GeneratedCode = GenerateActionCode(targetType, nodeData, errorHandling),
                    TargetType = targetType,
                    Parameters = ExtractActionParameters(nodeData, parameters),
                    HasErrorHandling = errorHandling
                };
                
                actionNodes.Add(actionNode);
            }
            
            return actionNodes;
        }
        
        private static List<GeneratedBehaviorNode> GenerateFallbackActionNodes(string description, string targetType, JObject parameters)
        {
            return new List<GeneratedBehaviorNode>
            {
                new GeneratedBehaviorNode
                {
                    Id = System.Guid.NewGuid().ToString(),
                    Name = $"FallbackAction_{targetType}",
                    NodeType = BehaviorNodeType.Action,
                    Description = $"Fallback action: {description}",
                    GeneratedCode = $"// Fallback {targetType} action implementation\n// Description: {description}",
                    Parameters = new Dictionary<string, object>()
                }
            };
        }
        
        private static string GenerateActionCode(string targetType, float[] nodeData, bool errorHandling)
        {
            var codeBuilder = new StringBuilder();
            codeBuilder.AppendLine($"// Generated {targetType} action code");
            codeBuilder.AppendLine($"// AI parameters: [{string.Join(", ", nodeData.Select(d => d.ToString("F2")))}]");
            
            if (errorHandling)
            {
                codeBuilder.AppendLine("try {");
            }
            
            codeBuilder.AppendLine($"    // {targetType} implementation");
            codeBuilder.AppendLine($"    var result = Execute{targetType}Action();");
            
            if (errorHandling)
            {
                codeBuilder.AppendLine("} catch (System.Exception ex) {");
                codeBuilder.AppendLine("    Debug.LogError($\"Action failed: {ex.Message}\");");
                codeBuilder.AppendLine("    return Status.Failure;");
                codeBuilder.AppendLine("}");
            }
            
            return codeBuilder.ToString();
        }
        
        private static Dictionary<string, object> ExtractActionParameters(float[] nodeData, JObject parameters)
        {
            var extractedParams = new Dictionary<string, object>();
            
            if (nodeData.Length >= 4)
            {
                extractedParams["intensity"] = nodeData[0];
                extractedParams["duration"] = nodeData[1];
                extractedParams["priority"] = nodeData[2];
                extractedParams["confidence"] = nodeData[3];
            }
            
            // Merge with original parameters
            if (parameters != null)
            {
                foreach (var prop in parameters.Properties())
                {
                    extractedParams[prop.Name] = prop.Value.ToObject<object>();
                }
            }
            
            return extractedParams;
        }

        /// <summary>
        /// [UNITY 6.2] - Cria nós de comportamento reais usando Unity Behavior API.
        /// </summary>
        private static List<GeneratedBehaviorNode> CreateRealBehaviorNodes(int count, List<string> types, string namingPattern, string aiDescription)
        {
            var nodes = new List<GeneratedBehaviorNode>();

            for (int i = 0; i < count; i++)
            {
                var nodeType = types != null && types.Count > 0 ? types[i % types.Count] : "Action";
                var nodeName = string.IsNullOrEmpty(namingPattern) ? $"BehaviorNode_{i + 1}" : namingPattern.Replace("{index}", (i + 1).ToString());

                var node = new GeneratedBehaviorNode
                {
                    Id = System.Guid.NewGuid().ToString(),
                    Name = nodeName,
                    NodeType = ParseNodeType(nodeType),
                    Description = GenerateNodeDescription(nodeType, aiDescription, i),
                    GeneratedCode = GenerateRealBehaviorCode(nodeType, nodeName, aiDescription),
                    Parameters = GenerateNodeParameters(nodeType, aiDescription)
                };

                nodes.Add(node);
            }

            return nodes;
        }

        /// <summary>
        /// [HELPER] - Gera descrição inteligente para o nó.
        /// </summary>
        private static string GenerateNodeDescription(string nodeType, string aiDescription, int index)
        {
            if (!string.IsNullOrEmpty(aiDescription))
            {
                return $"{nodeType} node for {aiDescription} (Node {index + 1})";
            }

            return nodeType.ToLower() switch
            {
                "action" => $"Executes a specific action behavior (Node {index + 1})",
                "condition" => $"Evaluates a condition for decision making (Node {index + 1})",
                "composite" => $"Manages multiple child behaviors (Node {index + 1})",
                "decorator" => $"Modifies child behavior execution (Node {index + 1})",
                _ => $"Generic {nodeType} behavior node (Node {index + 1})"
            };
        }

        /// <summary>
        /// [HELPER] - Gera código real de comportamento usando Unity 6.2 APIs.
        /// </summary>
        private static string GenerateRealBehaviorCode(string nodeType, string nodeName, string aiDescription)
        {
            var codeBuilder = new StringBuilder();

            // Adicionar using statements
            codeBuilder.AppendLine("using Unity.Behavior;");
            codeBuilder.AppendLine("using UnityEngine;");
            codeBuilder.AppendLine("using System;");
            codeBuilder.AppendLine();

            // Gerar classe baseada no tipo de nó
            switch (nodeType.ToLower())
            {
                case "action":
                    GenerateActionNodeCode(codeBuilder, nodeName, aiDescription);
                    break;
                case "condition":
                    GenerateConditionNodeCode(codeBuilder, nodeName, aiDescription);
                    break;
                case "composite":
                    GenerateCompositeNodeCode(codeBuilder, nodeName, aiDescription);
                    break;
                case "decorator":
                    GenerateDecoratorNodeCode(codeBuilder, nodeName, aiDescription);
                    break;
                default:
                    GenerateGenericNodeCode(codeBuilder, nodeName, aiDescription);
                    break;
            }

            return codeBuilder.ToString();
        }

        /// <summary>
        /// [HELPER] - Gera parâmetros para o nó baseado no tipo e descrição.
        /// </summary>
        private static Dictionary<string, object> GenerateNodeParameters(string nodeType, string aiDescription)
        {
            var parameters = new Dictionary<string, object>();

            // Parâmetros comuns
            parameters["Priority"] = 1.0f;
            parameters["Enabled"] = true;

            // Parâmetros específicos por tipo
            switch (nodeType.ToLower())
            {
                case "action":
                    parameters["Duration"] = 1.0f;
                    parameters["CanBeInterrupted"] = true;
                    break;
                case "condition":
                    parameters["CheckFrequency"] = 0.1f;
                    parameters["InvertResult"] = false;
                    break;
                case "composite":
                    parameters["ChildPolicy"] = "Sequential";
                    parameters["FailurePolicy"] = "StopOnFailure";
                    break;
                case "decorator":
                    parameters["RepeatCount"] = 1;
                    parameters["CooldownTime"] = 0.0f;
                    break;
            }

            // Adicionar parâmetros baseados na descrição AI
            if (!string.IsNullOrEmpty(aiDescription))
            {
                if (aiDescription.ToLower().Contains("movement"))
                {
                    parameters["Speed"] = 5.0f;
                    parameters["TargetDistance"] = 1.0f;
                }
                else if (aiDescription.ToLower().Contains("combat"))
                {
                    parameters["AttackRange"] = 2.0f;
                    parameters["Damage"] = 10.0f;
                }
                else if (aiDescription.ToLower().Contains("patrol"))
                {
                    parameters["PatrolRadius"] = 10.0f;
                    parameters["WaitTime"] = 2.0f;
                }
            }

            return parameters;
        }

        private static List<GeneratedBehaviorNode> CreatePlaceholderNodes(int count, List<string> types, string namingPattern)
        {
            var nodes = new List<GeneratedBehaviorNode>();

            for (int i = 0; i < count; i++)
            {
                var nodeType = types != null && types.Count > 0 ? types[i % types.Count] : "Action";
                var nodeName = string.IsNullOrEmpty(namingPattern) ? $"PlaceholderNode_{i + 1}" : $"{namingPattern}_{i + 1}";

                var node = new GeneratedBehaviorNode
                {
                    Id = System.Guid.NewGuid().ToString(),
                    Name = nodeName,
                    NodeType = ParseNodeType(nodeType),
                    Description = $"Placeholder {nodeType} node for future implementation",
                    GeneratedCode = GeneratePlaceholderCode(nodeType),
                    Parameters = new Dictionary<string, object>()
                };

                nodes.Add(node);
            }

            return nodes;
        }
        
        private static string GeneratePlaceholderCode(string nodeType)
        {
            var codeBuilder = new StringBuilder();
            codeBuilder.AppendLine($"// Placeholder {nodeType} implementation");
            codeBuilder.AppendLine($"// TODO: Implement {nodeType} logic");
            
            switch (nodeType.ToLower())
            {
                case "action":
                    codeBuilder.AppendLine("// Action node implementation");
                    codeBuilder.AppendLine("public override Status OnUpdate()");
                    codeBuilder.AppendLine("{");
                    codeBuilder.AppendLine("    // TODO: Implement action logic");
                    codeBuilder.AppendLine("    return Status.Success;");
                    codeBuilder.AppendLine("}");
                    break;
                    
                case "condition":
                    codeBuilder.AppendLine("// Condition node implementation");
                    codeBuilder.AppendLine("public override bool Evaluate()");
                    codeBuilder.AppendLine("{");
                    codeBuilder.AppendLine("    // TODO: Implement condition logic");
                    codeBuilder.AppendLine("    return true;");
                    codeBuilder.AppendLine("}");
                    break;
                    
                case "composite":
                    codeBuilder.AppendLine("// Composite node implementation");
                    codeBuilder.AppendLine("public override Status OnUpdate()");
                    codeBuilder.AppendLine("{");
                    codeBuilder.AppendLine("    // TODO: Implement composite logic");
                    codeBuilder.AppendLine("    return Status.Running;");
                    codeBuilder.AppendLine("}");
                    break;
                    
                default:
                    codeBuilder.AppendLine("// Generic node implementation");
                    codeBuilder.AppendLine("public override Status OnUpdate()");
                    codeBuilder.AppendLine("{");
                    codeBuilder.AppendLine("    // TODO: Implement node logic");
                    codeBuilder.AppendLine("    return Status.Success;");
                    codeBuilder.AppendLine("}");
                    break;
            }
            
            return codeBuilder.ToString();
        }

        /// <summary>
        /// [HELPER] - Gera código para Action Node.
        /// </summary>
        private static void GenerateActionNodeCode(StringBuilder codeBuilder, string nodeName, string aiDescription)
        {
            codeBuilder.AppendLine($"[System.Serializable]");
            codeBuilder.AppendLine($"public class {nodeName} : Action");
            codeBuilder.AppendLine("{");
            codeBuilder.AppendLine("    [SerializeField] private float duration = 1.0f;");
            codeBuilder.AppendLine("    [SerializeField] private bool canBeInterrupted = true;");
            codeBuilder.AppendLine();
            codeBuilder.AppendLine("    protected override Status OnStart()");
            codeBuilder.AppendLine("    {");
            codeBuilder.AppendLine($"        // {aiDescription ?? "Action implementation"}");
            codeBuilder.AppendLine("        return Status.Running;");
            codeBuilder.AppendLine("    }");
            codeBuilder.AppendLine();
            codeBuilder.AppendLine("    protected override Status OnUpdate()");
            codeBuilder.AppendLine("    {");
            codeBuilder.AppendLine("        // Execute action logic here");
            codeBuilder.AppendLine("        return Status.Success;");
            codeBuilder.AppendLine("    }");
            codeBuilder.AppendLine();
            codeBuilder.AppendLine("    protected override void OnEnd()");
            codeBuilder.AppendLine("    {");
            codeBuilder.AppendLine("        // Cleanup logic here");
            codeBuilder.AppendLine("    }");
            codeBuilder.AppendLine("}");
        }

        /// <summary>
        /// [HELPER] - Gera código para Condition Node.
        /// </summary>
        private static void GenerateConditionNodeCode(StringBuilder codeBuilder, string nodeName, string aiDescription)
        {
            codeBuilder.AppendLine($"[System.Serializable]");
            codeBuilder.AppendLine($"public class {nodeName} : Condition");
            codeBuilder.AppendLine("{");
            codeBuilder.AppendLine("    [SerializeField] private float checkFrequency = 0.1f;");
            codeBuilder.AppendLine("    [SerializeField] private bool invertResult = false;");
            codeBuilder.AppendLine();
            codeBuilder.AppendLine("    public override bool IsTrue()");
            codeBuilder.AppendLine("    {");
            codeBuilder.AppendLine($"        // {aiDescription ?? "Condition evaluation"}");
            codeBuilder.AppendLine("        bool result = EvaluateCondition();");
            codeBuilder.AppendLine("        return invertResult ? !result : result;");
            codeBuilder.AppendLine("    }");
            codeBuilder.AppendLine();
            codeBuilder.AppendLine("    private bool EvaluateCondition()");
            codeBuilder.AppendLine("    {");
            codeBuilder.AppendLine("        // Implement condition logic here");
            codeBuilder.AppendLine("        return true;");
            codeBuilder.AppendLine("    }");
            codeBuilder.AppendLine("}");
        }

        /// <summary>
        /// [HELPER] - Gera código para Composite Node.
        /// </summary>
        private static void GenerateCompositeNodeCode(StringBuilder codeBuilder, string nodeName, string aiDescription)
        {
            codeBuilder.AppendLine($"[System.Serializable]");
            codeBuilder.AppendLine($"public class {nodeName} : Composite");
            codeBuilder.AppendLine("{");
            codeBuilder.AppendLine("    [SerializeField] private string childPolicy = \"Sequential\";");
            codeBuilder.AppendLine("    [SerializeField] private string failurePolicy = \"StopOnFailure\";");
            codeBuilder.AppendLine();
            codeBuilder.AppendLine("    protected override Status OnUpdate()");
            codeBuilder.AppendLine("    {");
            codeBuilder.AppendLine($"        // {aiDescription ?? "Composite behavior management"}");
            codeBuilder.AppendLine("        return ExecuteChildren();");
            codeBuilder.AppendLine("    }");
            codeBuilder.AppendLine();
            codeBuilder.AppendLine("    private Status ExecuteChildren()");
            codeBuilder.AppendLine("    {");
            codeBuilder.AppendLine("        // Implement child execution logic here");
            codeBuilder.AppendLine("        return Status.Success;");
            codeBuilder.AppendLine("    }");
            codeBuilder.AppendLine("}");
        }

        /// <summary>
        /// [HELPER] - Gera código para Decorator Node.
        /// </summary>
        private static void GenerateDecoratorNodeCode(StringBuilder codeBuilder, string nodeName, string aiDescription)
        {
            codeBuilder.AppendLine($"[System.Serializable]");
            codeBuilder.AppendLine($"public class {nodeName} : Decorator");
            codeBuilder.AppendLine("{");
            codeBuilder.AppendLine("    [SerializeField] private int repeatCount = 1;");
            codeBuilder.AppendLine("    [SerializeField] private float cooldownTime = 0.0f;");
            codeBuilder.AppendLine();
            codeBuilder.AppendLine("    protected override Status OnUpdate()");
            codeBuilder.AppendLine("    {");
            codeBuilder.AppendLine($"        // {aiDescription ?? "Decorator behavior modification"}");
            codeBuilder.AppendLine("        return ModifyChildBehavior();");
            codeBuilder.AppendLine("    }");
            codeBuilder.AppendLine();
            codeBuilder.AppendLine("    private Status ModifyChildBehavior()");
            codeBuilder.AppendLine("    {");
            codeBuilder.AppendLine("        // Implement child behavior modification here");
            codeBuilder.AppendLine("        return Status.Success;");
            codeBuilder.AppendLine("    }");
            codeBuilder.AppendLine("}");
        }

        /// <summary>
        /// [HELPER] - Gera código genérico para nós.
        /// </summary>
        private static void GenerateGenericNodeCode(StringBuilder codeBuilder, string nodeName, string aiDescription)
        {
            codeBuilder.AppendLine($"[System.Serializable]");
            codeBuilder.AppendLine($"public class {nodeName} : BehaviorNode");
            codeBuilder.AppendLine("{");
            codeBuilder.AppendLine("    [SerializeField] private float priority = 1.0f;");
            codeBuilder.AppendLine();
            codeBuilder.AppendLine("    protected override Status OnUpdate()");
            codeBuilder.AppendLine("    {");
            codeBuilder.AppendLine($"        // {aiDescription ?? "Generic behavior implementation"}");
            codeBuilder.AppendLine("        return Status.Success;");
            codeBuilder.AppendLine("    }");
            codeBuilder.AppendLine("}");
        }

        /// <summary>
        /// [UNITY 6.2] - Adiciona nós ao grafo de comportamento.
        /// </summary>
        private static void AddNodesToGraph(BehaviorGraph graph, List<GeneratedBehaviorNode> nodes, bool autoConnect)
        {
            // Unity 6.2 Behavior Graph API - implementação simplificada
            // Em uma implementação real, você usaria as APIs específicas do Unity Behavior
            foreach (var node in nodes)
            {
                Debug.Log($"Adding node {node.Name} to behavior graph {graph.name}");
                // Aqui você adicionaria o nó ao grafo usando as APIs apropriadas
            }

            if (autoConnect && nodes.Count > 1)
            {
                Debug.Log($"Auto-connecting {nodes.Count} nodes in sequence");
                // Implementar lógica de conexão automática
            }
        }

        private static void AddPlaceholdersToGraph(BehaviorGraph graph, List<GeneratedBehaviorNode> placeholders, bool autoConnect)
        {
            // Implementação da adição de placeholders ao grafo
        }

        private static object AnalyzeBehaviorTreePerformance(BehaviorGraph graph)
        {
            // Implementação da análise de performance
            return new { };
        }

        private static object OptimizeWithAI(BehaviorGraph graph, List<string> goals, JObject targets, bool preserve, string level)
        {
            // Implementação da otimização com IA
            return new { NewMetrics = new { }, ImprovementPercentage = 0, OptimizationsApplied = new string[0] };
        }

        private static string SaveOptimizedBehaviorGraph(BehaviorGraph graph, string originalPath)
        {
            // Implementação do salvamento da versão otimizada
            return originalPath.Replace(".asset", "_optimized.asset");
        }

        private static List<ValidationResult> ValidateBehaviorCode(string codePath, List<string> rules, bool autoFix)
        {
            var results = new List<ValidationResult>();
            
            try
            {
                if (!File.Exists(codePath))
                {
                    results.Add(new ValidationResult
                    {
                        Type = ValidationType.FileNotFound,
                        Severity = ValidationSeverity.Error,
                        Message = $"Code file not found: {codePath}",
                        FilePath = codePath
                    });
                    return results;
                }
                
                var code = File.ReadAllText(codePath);
                
                // Syntax validation
                var syntaxErrors = ValidateCodeSyntax(code, codePath);
                results.AddRange(syntaxErrors);
                
                // Semantic validation
                var semanticErrors = ValidateCodeSemantics(code, codePath);
                results.AddRange(semanticErrors);
                
                // Custom rules validation
                if (rules != null && rules.Count > 0)
                {
                    var ruleViolations = ValidateCodeRules(code, rules, codePath);
                    results.AddRange(ruleViolations);
                }
                
                // Unity-specific validation
                var unityErrors = ValidateUnityBehaviorCode(code, codePath);
                results.AddRange(unityErrors);
                
                // Auto-fix if requested
                if (autoFix && results.Any(r => r.Severity == ValidationSeverity.Error))
                {
                    var fixedCode = ApplyAutoFixes(code, results);
                    if (fixedCode != code)
                    {
                        File.WriteAllText(codePath, fixedCode);
                        // Mark fixed issues
                        foreach (var result in results.Where(r => r.Severity == ValidationSeverity.Error))
                        {
                            result.AutoFixed = true;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                results.Add(new ValidationResult
                {
                    Type = ValidationType.ValidationError,
                    Severity = ValidationSeverity.Error,
                    Message = $"Validation error: {ex.Message}",
                    FilePath = codePath
                });
            }
            
            return results;
        }

        private static List<ValidationResult> ValidateBehaviorTree(string treePath, List<string> rules, bool autoFix)
        {
            var results = new List<ValidationResult>();
            
            try
            {
                if (!File.Exists(treePath))
                {
                    results.Add(new ValidationResult
                    {
                        Type = ValidationType.FileNotFound,
                        Severity = ValidationSeverity.Error,
                        Message = $"Behavior tree file not found: {treePath}",
                        FilePath = treePath
                    });
                    return results;
                }
                
                // Load behavior graph
                var behaviorGraph = AssetDatabase.LoadAssetAtPath<BehaviorGraph>(treePath);
                if (behaviorGraph == null)
                {
                    results.Add(new ValidationResult
                    {
                        Type = ValidationType.InvalidAsset,
                        Severity = ValidationSeverity.Error,
                        Message = $"Failed to load behavior graph: {treePath}",
                        FilePath = treePath
                    });
                    return results;
                }
                
                // Validate graph structure
                var structureErrors = ValidateGraphStructure(behaviorGraph, treePath);
                results.AddRange(structureErrors);
                
                // Validate node connections
                var connectionErrors = ValidateNodeConnections(behaviorGraph, treePath);
                results.AddRange(connectionErrors);
                
                // Validate node configurations
                var configErrors = ValidateNodeConfigurations(behaviorGraph, treePath);
                results.AddRange(configErrors);
                
                // Custom rules validation
                if (rules != null && rules.Count > 0)
                {
                    var ruleViolations = ValidateTreeRules(behaviorGraph, rules, treePath);
                    results.AddRange(ruleViolations);
                }
                
                // Performance validation
                var performanceWarnings = ValidateTreePerformance(behaviorGraph, treePath);
                results.AddRange(performanceWarnings);
                
                // Auto-fix if requested
                if (autoFix && results.Any(r => r.Severity == ValidationSeverity.Error && r.CanAutoFix))
                {
                    ApplyTreeAutoFixes(behaviorGraph, results);
                    EditorUtility.SetDirty(behaviorGraph);
                    AssetDatabase.SaveAssets();
                }
            }
            catch (Exception ex)
            {
                results.Add(new ValidationResult
                {
                    Type = ValidationType.ValidationError,
                    Severity = ValidationSeverity.Error,
                    Message = $"Tree validation error: {ex.Message}",
                    FilePath = treePath
                });
            }
            
            return results;
        }

        private static string GenerateValidationReport(List<ValidationResult> results, string codePath, string treePath)
        {
            try
            {
                var reportDir = "Assets/ValidationReports";
                if (!Directory.Exists(reportDir))
                {
                    Directory.CreateDirectory(reportDir);
                }
                
                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var reportPath = Path.Combine(reportDir, $"validation_report_{timestamp}.txt");
                
                var reportBuilder = new StringBuilder();
                reportBuilder.AppendLine("=== BEHAVIOR AI VALIDATION REPORT ===");
                reportBuilder.AppendLine($"Generated: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                reportBuilder.AppendLine();
                
                if (!string.IsNullOrEmpty(codePath))
                {
                    reportBuilder.AppendLine($"Code Path: {codePath}");
                }
                
                if (!string.IsNullOrEmpty(treePath))
                {
                    reportBuilder.AppendLine($"Tree Path: {treePath}");
                }
                
                reportBuilder.AppendLine();
                reportBuilder.AppendLine("=== SUMMARY ===");
                reportBuilder.AppendLine($"Total Issues: {results.Count}");
                reportBuilder.AppendLine($"Errors: {results.Count(r => r.Severity == ValidationSeverity.Error)}");
                reportBuilder.AppendLine($"Warnings: {results.Count(r => r.Severity == ValidationSeverity.Warning)}");
                reportBuilder.AppendLine($"Info: {results.Count(r => r.Severity == ValidationSeverity.Info)}");
                reportBuilder.AppendLine($"Auto-Fixed: {results.Count(r => r.AutoFixed)}");
                reportBuilder.AppendLine();
                
                if (results.Any())
                {
                    reportBuilder.AppendLine("=== DETAILED RESULTS ===");
                    
                    var groupedResults = results.GroupBy(r => r.Severity).OrderBy(g => g.Key);
                    
                    foreach (var group in groupedResults)
                    {
                        reportBuilder.AppendLine($"\n--- {group.Key.ToString().ToUpper()} ---");
                        
                        foreach (var result in group)
                        {
                            reportBuilder.AppendLine($"[{result.Type}] {result.Message}");
                            if (!string.IsNullOrEmpty(result.FilePath))
                            {
                                reportBuilder.AppendLine($"  File: {result.FilePath}");
                            }
                            if (result.LineNumber > 0)
                            {
                                reportBuilder.AppendLine($"  Line: {result.LineNumber}");
                            }
                            if (result.AutoFixed)
                            {
                                reportBuilder.AppendLine($"  Status: AUTO-FIXED");
                            }
                            reportBuilder.AppendLine();
                        }
                    }
                }
                else
                {
                    reportBuilder.AppendLine("=== NO ISSUES FOUND ===");
                    reportBuilder.AppendLine("All validations passed successfully!");
                }
                
                reportBuilder.AppendLine();
                reportBuilder.AppendLine("=== END OF REPORT ===");
                
                File.WriteAllText(reportPath, reportBuilder.ToString());
                AssetDatabase.Refresh();
                
                return reportPath;
            }
            catch (Exception ex)
            {
                Debug.LogError($"Failed to generate validation report: {ex.Message}");
                return null;
            }
        }

        #endregion
        
        #region Validation Helper Methods
        
        private static List<ValidationResult> ValidateCodeSyntax(string code, string filePath)
        {
            var results = new List<ValidationResult>();
            
            if (string.IsNullOrWhiteSpace(code))
            {
                results.Add(new ValidationResult
                {
                    Type = ValidationType.SyntaxError,
                    Severity = ValidationSeverity.Error,
                    Message = "Code is empty or null",
                    FilePath = filePath,
                    CanAutoFix = false
                });
                return results;
            }
            
            var lines = code.Split('\n');
            var braceCount = 0;
            var parenthesesCount = 0;
            
            for (int i = 0; i < lines.Length; i++)
            {
                var line = lines[i].Trim();
                var lineNumber = i + 1;
                
                // Count braces and parentheses
                braceCount += line.Count(c => c == '{') - line.Count(c => c == '}');
                parenthesesCount += line.Count(c => c == '(') - line.Count(c => c == ')');
                
                // Check for common syntax errors
                if (line.Contains(";") && line.Count(c => c == ';') > 1 && !line.Contains("for"))
                {
                    results.Add(new ValidationResult
                    {
                        Type = ValidationType.SyntaxError,
                        Severity = ValidationSeverity.Warning,
                        Message = "Multiple semicolons detected",
                        FilePath = filePath,
                        LineNumber = lineNumber,
                        CanAutoFix = true
                    });
                }
                
                // Check for missing semicolons
                if (line.EndsWith(")") && !line.Contains("if") && !line.Contains("while") && !line.Contains("for") && !line.Contains("{"))
                {
                    if (!line.EndsWith(";") && !line.EndsWith("{") && !line.EndsWith("}"))
                    {
                        results.Add(new ValidationResult
                        {
                            Type = ValidationType.SyntaxError,
                            Severity = ValidationSeverity.Error,
                            Message = "Missing semicolon",
                            FilePath = filePath,
                            LineNumber = lineNumber,
                            CanAutoFix = true
                        });
                    }
                }
            }
            
            if (braceCount != 0)
            {
                results.Add(new ValidationResult
                {
                    Type = ValidationType.SyntaxError,
                    Severity = ValidationSeverity.Error,
                    Message = $"Mismatched braces: {Math.Abs(braceCount)} unmatched",
                    FilePath = filePath,
                    CanAutoFix = false
                });
            }
            
            if (parenthesesCount != 0)
            {
                results.Add(new ValidationResult
                {
                    Type = ValidationType.SyntaxError,
                    Severity = ValidationSeverity.Error,
                    Message = $"Mismatched parentheses: {Math.Abs(parenthesesCount)} unmatched",
                    FilePath = filePath,
                    CanAutoFix = false
                });
            }
            
            return results;
        }
        
        private static List<ValidationResult> ValidateCodeSemantics(string code, string filePath)
        {
            var results = new List<ValidationResult>();
            
            // Check for required method signatures
            if (code.Contains("OnUpdate") && !code.Contains("Status"))
            {
                results.Add(new ValidationResult
                {
                    Type = ValidationType.SemanticError,
                    Severity = ValidationSeverity.Error,
                    Message = "OnUpdate methods should return Status type",
                    FilePath = filePath,
                    CanAutoFix = false
                });
            }
            
            if (code.Contains("Evaluate") && !code.Contains("bool"))
            {
                results.Add(new ValidationResult
                {
                    Type = ValidationType.SemanticError,
                    Severity = ValidationSeverity.Error,
                    Message = "Evaluate methods should return bool type",
                    FilePath = filePath,
                    CanAutoFix = false
                });
            }
            
            // Check for Unity.Behavior namespace usage
            if (!code.Contains("Unity.Behavior") && !code.Contains("using Unity.Behavior"))
            {
                results.Add(new ValidationResult
                {
                    Type = ValidationType.SemanticError,
                    Severity = ValidationSeverity.Warning,
                    Message = "Code should use Unity.Behavior namespace",
                    FilePath = filePath,
                    CanAutoFix = true
                });
            }
            
            return results;
        }
        
        private static List<ValidationResult> ValidateCodeRules(string code, List<string> rules, string filePath)
        {
            var results = new List<ValidationResult>();
            
            foreach (var rule in rules)
            {
                switch (rule.ToLower())
                {
                    case "no_debug_log":
                        if (code.Contains("Debug.Log"))
                        {
                            results.Add(new ValidationResult
                            {
                                Type = ValidationType.CustomRuleViolation,
                                Severity = ValidationSeverity.Warning,
                                Message = "Debug.Log statements not allowed",
                                FilePath = filePath,
                                CanAutoFix = true
                            });
                        }
                        break;
                        
                    case "require_error_handling":
                        if (!code.Contains("try") && !code.Contains("catch"))
                        {
                            results.Add(new ValidationResult
                            {
                                Type = ValidationType.CustomRuleViolation,
                                Severity = ValidationSeverity.Warning,
                                Message = "Error handling required",
                                FilePath = filePath,
                                CanAutoFix = false
                            });
                        }
                        break;
                        
                    case "require_documentation":
                        if (!code.Contains("///") && !code.Contains("//"))
                        {
                            results.Add(new ValidationResult
                            {
                                Type = ValidationType.CustomRuleViolation,
                                Severity = ValidationSeverity.Info,
                                Message = "Code documentation recommended",
                                FilePath = filePath,
                                CanAutoFix = false
                            });
                        }
                        break;
                }
            }
            
            return results;
        }
        
        private static List<ValidationResult> ValidateUnityBehaviorCode(string code, string filePath)
        {
            var results = new List<ValidationResult>();
            
            // Check for Unity.Behavior specific requirements
            if (code.Contains("BehaviorNode") && !code.Contains("Unity.Behavior"))
            {
                results.Add(new ValidationResult
                {
                    Type = ValidationType.UnitySpecificError,
                    Severity = ValidationSeverity.Error,
                    Message = "BehaviorNode usage requires Unity.Behavior namespace",
                    FilePath = filePath,
                    CanAutoFix = true
                });
            }
            
            // Check for proper Unity API usage
            if (code.Contains("GameObject") && !code.Contains("UnityEngine"))
            {
                results.Add(new ValidationResult
                {
                    Type = ValidationType.UnitySpecificError,
                    Severity = ValidationSeverity.Warning,
                    Message = "GameObject usage should include UnityEngine namespace",
                    FilePath = filePath,
                    CanAutoFix = true
                });
            }
            
            return results;
        }
        
        private static string ApplyAutoFixes(string code, List<ValidationResult> results)
        {
            var fixedCode = code;
            
            foreach (var result in results.Where(r => r.CanAutoFix))
            {
                switch (result.Type)
                {
                    case ValidationType.SyntaxError:
                        if (result.Message.Contains("Missing semicolon"))
                        {
                            // Simple auto-fix for missing semicolons
                            var lines = fixedCode.Split('\n');
                            if (result.LineNumber > 0 && result.LineNumber <= lines.Length)
                            {
                                var line = lines[result.LineNumber - 1];
                                if (!line.TrimEnd().EndsWith(";") && !line.TrimEnd().EndsWith("{") && !line.TrimEnd().EndsWith("}"))
                                {
                                    lines[result.LineNumber - 1] = line.TrimEnd() + ";";
                                    fixedCode = string.Join("\n", lines);
                                }
                            }
                        }
                        break;
                        
                    case ValidationType.CustomRuleViolation:
                        if (result.Message.Contains("Debug.Log"))
                        {
                            fixedCode = fixedCode.Replace("Debug.Log", "// Debug.Log");
                        }
                        break;
                        
                    case ValidationType.UnitySpecificError:
                    case ValidationType.SemanticError:
                        if (result.Message.Contains("Unity.Behavior namespace"))
                        {
                            if (!fixedCode.Contains("using Unity.Behavior;"))
                            {
                                fixedCode = "using Unity.Behavior;\n" + fixedCode;
                            }
                        }
                        if (result.Message.Contains("UnityEngine namespace"))
                        {
                            if (!fixedCode.Contains("using UnityEngine;"))
                            {
                                fixedCode = "using UnityEngine;\n" + fixedCode;
                            }
                        }
                        break;
                }
            }
            
            return fixedCode;
        }
        
        private static List<ValidationResult> ValidateGraphStructure(BehaviorGraph graph, string filePath)
        {
            var results = new List<ValidationResult>();
            
            try
            {
                // Fix: Since BehaviorGraph doesn't have direct Nodes property in Unity 6.2
                // we'll do basic validation without accessing internal structure
                
                if (graph == null)
                {
                    results.Add(new ValidationResult
                    {
                        Type = ValidationType.StructureError,
                        Severity = ValidationSeverity.Error,
                        Message = "Behavior graph is null",
                        FilePath = filePath,
                        CanAutoFix = false
                    });
                    return results;
                }

                // Basic validation - graph exists and has a name
                if (string.IsNullOrEmpty(graph.name))
                {
                    results.Add(new ValidationResult
                    {
                        Type = ValidationType.StructureError,
                        Severity = ValidationSeverity.Warning,
                        Message = "Behavior graph has no name",
                        FilePath = filePath,
                        CanAutoFix = true
                    });
                }

                // Validate graph structure (simplified)
                // In a real implementation, you'd need to access the graph's internal structure
                // through appropriate Unity Behavior APIs
                
            }
            catch (Exception e)
            {
                results.Add(new ValidationResult
                {
                    Type = ValidationType.StructureError,
                    Severity = ValidationSeverity.Error,
                    Message = $"Error validating graph structure: {e.Message}",
                    FilePath = filePath,
                    CanAutoFix = false
                });
            }
            
            return results;
        }
        
        private static List<ValidationResult> ValidateNodeConnections(BehaviorGraph graph, string filePath)
        {
            var results = new List<ValidationResult>();
            
            try
            {
                // Fix: Simplified validation since we can't access Edges property directly
                if (graph == null)
                {
                    results.Add(new ValidationResult
                    {
                        Type = ValidationType.ConnectionError,
                        Severity = ValidationSeverity.Error,
                        Message = "Cannot validate connections - graph is null",
                        FilePath = filePath,
                        CanAutoFix = false
                    });
                }
                
                // Additional connection validation would go here
                // This is a placeholder since Unity 6.2 Behavior Graph API is limited
                
            }
            catch (Exception e)
            {
                results.Add(new ValidationResult
                {
                    Type = ValidationType.ConnectionError,
                    Severity = ValidationSeverity.Error,
                    Message = $"Error validating connections: {e.Message}",
                    FilePath = filePath,
                    CanAutoFix = false
                });
            }
            
            return results;
        }
        
        private static List<ValidationResult> ValidateNodeConfigurations(BehaviorGraph graph, string filePath)
        {
            var results = new List<ValidationResult>();
            
            try
            {
                // Fix: Simplified validation for node configurations
                if (graph == null)
                {
                    results.Add(new ValidationResult
                    {
                        Type = ValidationType.ConfigurationError,
                        Severity = ValidationSeverity.Error,
                        Message = "Cannot validate node configurations - graph is null",
                        FilePath = filePath,
                        CanAutoFix = false
                    });
                }
                
                // Additional node configuration validation would go here
                
            }
            catch (Exception e)
            {
                results.Add(new ValidationResult
                {
                    Type = ValidationType.ConfigurationError,
                    Severity = ValidationSeverity.Error,
                    Message = $"Error validating node configurations: {e.Message}",
                    FilePath = filePath,
                    CanAutoFix = false
                });
            }
            
            return results;
        }
        
        private static List<ValidationResult> ValidateTreeRules(BehaviorGraph graph, List<string> rules, string filePath)
        {
            var results = new List<ValidationResult>();
            
            try
            {
                if (graph == null)
                {
                    results.Add(new ValidationResult
                    {
                        Type = ValidationType.CustomRuleViolation,
                        Severity = ValidationSeverity.Error,
                        Message = "Cannot validate tree rules - graph is null",
                        FilePath = filePath,
                        CanAutoFix = false
                    });
                    return results;
                }

                // Validate custom rules
                foreach (var rule in rules ?? new List<string>())
                {
                    // Fix: Simple rule validation
                    if (rule.Contains("max_depth"))
                    {
                        var maxDepth = CalculateTreeDepth(graph);
                        if (maxDepth > 10) // Example threshold
                        {
                            results.Add(new ValidationResult
                            {
                                Type = ValidationType.CustomRuleViolation,
                                Severity = ValidationSeverity.Warning,
                                Message = $"Tree depth ({maxDepth}) exceeds recommended maximum",
                                FilePath = filePath,
                                CanAutoFix = false
                            });
                        }
                    }
                    
                    if (rule.Contains("performance"))
                    {
                        var performance = EstimatePerformance(graph);
                        if (performance < 0.5f)
                        {
                            results.Add(new ValidationResult
                            {
                                Type = ValidationType.CustomRuleViolation,
                                Severity = ValidationSeverity.Warning,
                                Message = $"Estimated performance ({performance:F2}) is below threshold",
                                FilePath = filePath,
                                CanAutoFix = false
                            });
                        }
                    }
                }
                
            }
            catch (Exception e)
            {
                results.Add(new ValidationResult
                {
                    Type = ValidationType.CustomRuleViolation,
                    Severity = ValidationSeverity.Error,
                    Message = $"Error validating tree rules: {e.Message}",
                    FilePath = filePath,
                    CanAutoFix = false
                });
            }
            
            return results;
        }
        
        private static List<ValidationResult> ValidateTreePerformance(BehaviorGraph graph, string filePath)
        {
            var results = new List<ValidationResult>();
            
            try
            {
                if (graph == null)
                {
                    results.Add(new ValidationResult
                    {
                        Type = ValidationType.PerformanceWarning,
                        Severity = ValidationSeverity.Error,
                        Message = "Cannot validate performance - graph is null",
                        FilePath = filePath,
                        CanAutoFix = false
                    });
                    return results;
                }

                // Estimate performance impact
                var performance = EstimatePerformance(graph);
                
                if (performance < 0.3f)
                {
                    results.Add(new ValidationResult
                    {
                        Type = ValidationType.PerformanceWarning,
                        Severity = ValidationSeverity.Error,
                        Message = "Poor performance detected - consider optimizing the behavior tree",
                        FilePath = filePath,
                        CanAutoFix = false
                    });
                }
                else if (performance < 0.6f)
                {
                    results.Add(new ValidationResult
                    {
                        Type = ValidationType.PerformanceWarning,
                        Severity = ValidationSeverity.Warning,
                        Message = "Moderate performance issues detected",
                        FilePath = filePath,
                        CanAutoFix = false
                    });
                }
                
            }
            catch (Exception e)
            {
                results.Add(new ValidationResult
                {
                    Type = ValidationType.PerformanceWarning,
                    Severity = ValidationSeverity.Error,
                    Message = $"Error validating performance: {e.Message}",
                    FilePath = filePath,
                    CanAutoFix = false
                });
            }
            
            return results;
        }
        
        private static void ApplyTreeAutoFixes(BehaviorGraph graph, List<ValidationResult> results)
        {
            // Apply automatic fixes to the behavior tree
            foreach (var result in results.Where(r => r.CanAutoFix))
            {
                try
                {
                    // Fix: Switch expression needs to be based on a value, not method group
                    switch (result.Type)
                    {
                        case ValidationType.SyntaxError:
                            // Apply syntax fixes
                            Debug.Log($"Auto-fixing syntax error: {result.Message}");
                            break;
                            
                        case ValidationType.StructureError:
                            // Apply structure fixes
                            if (string.IsNullOrEmpty(graph.name))
                            {
                                graph.name = "FixedBehaviorGraph";
                            }
                            break;
                            
                        case ValidationType.CustomRuleViolation:
                            // Apply custom rule fixes
                            Debug.Log($"Auto-fixing rule violation: {result.Message}");
                            break;
                            
                        case ValidationType.UnitySpecificError:
                        case ValidationType.SemanticError:
                        case ValidationType.ConfigurationError:
                            // Apply other fixes
                            Debug.Log($"Auto-fixing error: {result.Message}");
                            break;
                    }
                    
                    result.AutoFixed = true;
                }
                catch (Exception e)
                {
                    Debug.LogError($"Failed to auto-fix {result.Type}: {e.Message}");
                }
            }
        }
        
        private static float CalculateTreeDepth(BehaviorGraph graph)
        {
            // Simple implementation for tree depth calculation
            // In Unity 6.2, BehaviorGraph doesn't have direct Nodes property
            // This is a placeholder implementation
            return 5; // Default depth
        }
        
        private static float EstimatePerformance(BehaviorGraph graph)
        {
            // Simple performance estimation
            // In Unity 6.2, this would require custom analysis
            return 0.75f; // Default performance score
        }
        
        #endregion
    }
}